import React, { useState, useEffect } from 'react';
import { Bell, Eye, Upload, Download, MessageCircle, Search, Plus, LogOut, Calendar, DollarSign, Clock, CheckCircle, AlertCircle, XCircle, Pause, ArrowRight, TrendingUp, Activity, FileText, Users, Lock, Shield } from 'lucide-react';

// Mock data - now will be managed by component state for reactivity
const initialMockUsers = {
  'archive001': { id: 1, username: 'archive001', name: 'Sarah Archive', role: 'archive', email: '<EMAIL>', avatar: '👩‍💼' },
  'admin001': { id: 2, username: 'admin001', name: 'John Administrator', role: 'loan_admin', email: '<EMAIL>', avatar: '👨‍💼' },
  'ops_a': { id: 3, username: 'ops_a', name: 'Ahmed Operations', role: 'operations', email: '<EMAIL>', avatar: '👨‍🔧', region: 'North Africa' },
  'ops_b': { id: 4, username: 'ops_b', name: 'Fatima Operations', role: 'operations', email: '<EMAIL>', avatar: '👩‍🔧', region: 'Central Africa' },
  'ops_c': { id: 5, username: 'ops_c', name: 'Chen Operations', role: 'operations', email: '<EMAIL>', avatar: '👨‍💼', region: 'South East Asia' },
  'ops_d': { id: 6, username: 'ops_d', name: 'Amira Operations', role: 'operations', email: '<EMAIL>', avatar: '👩‍💼', region: 'Central Asia' },
  'bank001': { id: 7, username: 'bank001', name: 'Lisa Banking', role: 'core_banking', email: '<EMAIL>', avatar: '👩‍💻' }
};

// Regional mapping for automatic assignment
const regionMapping = {
  'Egypt': 'North Africa',
  'Libya': 'North Africa',
  'Tunisia': 'North Africa',
  'Algeria': 'North Africa',
  'Morocco': 'North Africa',
  'Sudan': 'North Africa',
  'Chad': 'Central Africa',
  'Central African Republic': 'Central Africa',
  'Democratic Republic of Congo': 'Central Africa',
  'Cameroon': 'Central Africa',
  'Nigeria': 'Central Africa',
  'Kenya': 'Central Africa',
  'Malaysia': 'South East Asia',
  'Indonesia': 'South East Asia',
  'Thailand': 'South East Asia',
  'Vietnam': 'South East Asia',
  'Philippines': 'South East Asia',
  'Singapore': 'South East Asia',
  'Kazakhstan': 'Central Asia',
  'Uzbekistan': 'Central Asia',
  'Kyrgyzstan': 'Central Asia',
  'Tajikistan': 'Central Asia',
  'Afghanistan': 'Central Asia',
  'Pakistan': 'Central Asia'
};

// OCR Simulation - extracts data from Abu Dhabi Fund withdrawal request format
const simulateOCRExtraction = (filename) => {
  // Simulate different extraction results based on filename
  const mockExtractions = {
    'withdrawal_egypt_001.pdf': {
      projectNumber: 'EGY-2024-001',
      country: 'Egypt',
      refNumber: 'WR/EGY/001/2024',
      beneficiaryName: 'Egyptian Infrastructure Development Company',
      amount: '2500000',
      currency: 'USD',
      valueDate: '2025-08-15',
      priority: 'high',
      beneficiaryBank: 'National Bank of Egypt',
      bankAccount: '1234-5678-9012-3456',
      iban: 'EG38-0019-0005-0000-0000-2631-8000-2',
      swiftCode: 'NBEGEGCX',
      agreementDate: '2024-03-15'
    },
    'withdrawal_malaysia_002.pdf': {
      projectNumber: 'MYS-2024-002',
      country: 'Malaysia',
      refNumber: 'WR/MYS/002/2024',
      beneficiaryName: 'Malaysia Sustainable Energy Solutions',
      amount: '1800000',
      currency: 'USD',
      valueDate: '2025-07-20',
      priority: 'medium',
      beneficiaryBank: 'Maybank Malaysia',
      bankAccount: '5678-9012-3456-7890',
      iban: 'MY97-1234-5678-9012-3456-7890',
      swiftCode: 'MBBEMYKL',
      agreementDate: '2024-02-10'
    },
    'withdrawal_uzbekistan_003.pdf': {
      projectNumber: 'UZB-2024-003',
      country: 'Uzbekistan',
      refNumber: 'WR/UZB/003/2024',
      beneficiaryName: 'Uzbekistan Agricultural Development Corp',
      amount: '1200000',
      currency: 'USD',
      valueDate: '2025-09-10',
      priority: 'medium',
      beneficiaryBank: 'National Bank of Uzbekistan',
      bankAccount: '9876-5432-1098-7654',
      iban: 'UZ21-0012-3456-7890-1234-5678',
      swiftCode: 'NBUZUZAA',
      agreementDate: '2024-01-20'
    },
    'withdrawal_nigeria_004.pdf': {
      projectNumber: 'NGA-2024-004',
      country: 'Nigeria',
      refNumber: 'WR/NGA/004/2024',
      beneficiaryName: 'Nigeria Power Infrastructure Limited',
      amount: '3200000',
      currency: 'USD',
      valueDate: '2025-08-05',
      priority: 'urgent',
      beneficiaryBank: 'First Bank of Nigeria',
      bankAccount: '2468-1357-9024-6810',
      iban: 'NG21-0123-4567-8901-2345-6789',
      swiftCode: 'FBNINGLA',
      agreementDate: '2024-04-05'
    }
  };

  // Return mock data or generate random data
  return mockExtractions[filename] || {
    projectNumber: `GEN-2024-${Math.floor(Math.random() * 100).toString().padStart(3, '0')}`,
    country: Object.keys(regionMapping)[Math.floor(Math.random() * Object.keys(regionMapping).length)],
    refNumber: `WR/GEN/${Math.floor(Math.random() * 1000)}/2024`,
    beneficiaryName: 'Sample Beneficiary Company',
    amount: (Math.floor(Math.random() * 3000000) + 500000).toString(),
    currency: 'USD',
    valueDate: '2025-08-01',
    priority: 'medium',
    beneficiaryBank: 'Sample National Bank',
    bankAccount: '0000-0000-0000-0000',
    iban: 'XX00-0000-0000-0000-0000-0000',
    swiftCode: 'SMPLXXXX',
    agreementDate: '2024-01-01'
  };
};

// Function to get operations team member by region
const getOperationsTeamByRegion = (country) => {
  const region = regionMapping[country];
  const operationsTeam = Object.values(initialMockUsers).filter(user => user.role === 'operations');
  return operationsTeam.find(user => user.region === region) || operationsTeam[0];
};

const initialMockRequests = [
  {
    id: 1001,
    projectNumber: 'UAE-2024-001',
    country: 'Egypt',
    refNumber: 'REF/EGY/001',
    beneficiaryName: 'Egyptian Infrastructure Company',
    amount: 1200000,
    currency: 'USD',
    valueDate: '2025-05-12',
    status: 'Disbursed',
    currentStage: 'disbursed',
    assignedTo: 7, // Lisa Banking (correct ID)
    createdBy: 1,
    createdAt: '2025-05-10T09:00:00Z',
    updatedAt: '2025-05-15T14:30:00:00Z',
    processingDays: 5,
    priority: 'high',
    region: 'North Africa'
  },
  {
    id: 1002,
    projectNumber: 'MYS-2024-002',
    country: 'Malaysia',
    refNumber: 'REF/MYS/002',
    beneficiaryName: 'Malaysia Energy Solutions',
    amount: 850000,
    currency: 'EUR',
    valueDate: '2025-05-07',
    status: 'Pending due to expired withdrawal date; extension request sent to legal for review',
    currentStage: 'technical_review',
    assignedTo: 5, // Chen Operations (South East Asia)
    createdBy: 1,
    createdAt: '2025-05-05T10:15:00Z',
    updatedAt: '2025-05-08T16:20:00Z',
    processingDays: 15,
    priority: 'urgent',
    region: 'South East Asia'
  },
  {
    id: 1003,
    projectNumber: 'UZB-2024-003',
    country: 'Uzbekistan',
    refNumber: 'REF/UZB/003',
    beneficiaryName: 'Uzbekistan Agricultural Corp',
    amount: 550000,
    currency: 'USD',
    valueDate: '2025-05-13',
    status: 'Pending with Operations for technical approval',
    currentStage: 'technical_review',
    assignedTo: 6, // Amira Operations (Central Asia)
    createdBy: 1,
    createdAt: '2025-05-11T11:30:00Z',
    updatedAt: '2025-05-12T09:45:00Z',
    processingDays: 8,
    priority: 'medium',
    region: 'Central Asia'
  },
  {
    id: 1004,
    projectNumber: 'NGA-2024-004',
    country: 'Nigeria',
    refNumber: 'REF/NGA/004',
    beneficiaryName: 'Nigeria Power Infrastructure Ltd',
    amount: 980000,
    currency: 'USD',
    valueDate: '2025-05-15',
    status: 'Awaiting Core Banking disbursement processing',
    currentStage: 'core_banking',
    assignedTo: 7, // Lisa Banking (correct ID)
    createdBy: 1,
    createdAt: '2025-05-13T14:20:00Z',
    updatedAt: '2025-05-14T11:10:00Z',
    processingDays: 6,
    priority: 'high',
    region: 'Central Africa'
  },
  {
    id: 1005,
    projectNumber: 'EGY-2024-005',
    country: 'Egypt',
    refNumber: 'REF/EGY/005',
    beneficiaryName: 'Egyptian Solar Energy Co',
    amount: 1500000,
    currency: 'EUR',
    valueDate: '2025-05-13',
    status: 'Ready for disbursement - Core Banking processing',
    currentStage: 'core_banking',
    assignedTo: 7, // Lisa Banking (correct ID)
    createdBy: 1,
    createdAt: '2025-05-11T16:45:00Z',
    updatedAt: '2025-05-13T13:25:00Z',
    processingDays: 7,
    priority: 'medium',
    region: 'North Africa'
  },
  {
    id: 1006,
    projectNumber: 'THA-2024-006',
    country: 'Thailand',
    refNumber: 'REF/THA/006',
    beneficiaryName: 'Thailand Infrastructure Development',
    amount: 600000,
    currency: 'USD',
    valueDate: '2025-05-13',
    status: 'Sent back to beneficiary for missing documentation',
    currentStage: 'initial_review',
    assignedTo: 2,
    createdBy: 1,
    createdAt: '2025-05-12T08:30:00Z',
    updatedAt: '2025-05-13T10:15:00Z',
    processingDays: 9,
    priority: 'low',
    region: 'South East Asia'
  },
  {
    id: 1007,
    projectNumber: 'KAZ-2024-007',
    country: 'Kazakhstan',
    refNumber: 'REF/KAZ/007',
    beneficiaryName: 'Kazakhstan Mining Solutions',
    amount: 720000,
    currency: 'USD',
    valueDate: '2025-05-15',
    status: 'Disbursed',
    currentStage: 'disbursed',
    assignedTo: 7, // Lisa Banking (correct ID)
    createdBy: 1,
    createdAt: '2025-05-13T12:00:00Z',
    updatedAt: '2025-05-15T15:45:00Z',
    processingDays: 2,
    priority: 'medium',
    region: 'Central Asia'
  },
  {
    id: 1008,
    projectNumber: 'MAR-2024-008',
    country: 'Morocco',
    refNumber: 'REF/MAR/008',
    beneficiaryName: 'Morocco Renewable Energy Ltd',
    amount: 450000,
    currency: 'USD',
    valueDate: '2025-05-14',
    status: 'Ready for technical review',
    currentStage: 'initial_review',
    assignedTo: 2,
    createdBy: 1,
    createdAt: '2025-05-12T15:20:00Z',
    updatedAt: '2025-05-13T14:30:00Z',
    processingDays: 6,
    priority: 'medium',
    region: 'North Africa'
  }
];

const initialMockDocuments = {
  1001: [
    { id: 1, filename: 'Withdrawal_Form_REF001.pdf', fileSize: '2.3 MB', uploadedAt: '2025-05-10T09:15:00Z', uploadedBy: 1 },
    { id: 2, filename: 'Invoice_Alpha_001.pdf', fileSize: '1.8 MB', uploadedAt: '2025-05-10T09:16:00Z', uploadedBy: 1 },
    { id: 3, filename: 'Supporting_Documents.pdf', fileSize: '3.1 MB', uploadedAt: '2025-05-10T09:17:00Z', uploadedBy: 1 }
  ],
  1002: [
    { id: 4, filename: 'Withdrawal_Form_REF002.pdf', fileSize: '2.1 MB', uploadedAt: '2025-05-05T10:20:00Z', uploadedBy: 1 },
    { id: 5, filename: 'Invoice_Beta_001.pdf', fileSize: '1.9 MB', uploadedAt: '2025-05-05T10:21:00Z', uploadedBy: 1 },
    { id: 6, filename: 'Authorization_Letter.pdf', fileSize: '0.8 MB', uploadedAt: '2025-05-05T10:22:00Z', uploadedBy: 1 }
  ]
};

const initialMockComments = {
  1002: [
    { id: 1, userId: 3, comment: 'Withdrawal date has expired. Requesting extension from legal team.', createdAt: '2025-05-08T14:30:00Z' },
    { id: 2, userId: 2, comment: 'Extension request documentation sent to beneficiary.', createdAt: '2025-05-08T16:20:00Z' }
  ],
  1003: [
    { id: 3, userId: 3, comment: 'Technical review in progress. Checking project eligibility requirements.', createdAt: '2025-05-12T10:15:00Z' }
  ]
};

const initialMockAuditLog = {
  1002: [
    { id: 1, userId: 1, action: 'Request created', timestamp: '2025-05-05T10:15:00Z' },
    { id: 2, userId: 1, action: 'Documents uploaded', timestamp: '2025-05-05T10:22:00Z' },
    { id: 3, userId: 2, action: 'Request details populated', timestamp: '2025-05-06T09:30:00Z' },
    { id: 4, userId: 2, action: 'Status updated to Technical Review', timestamp: '2025-05-06T09:35:00Z' },
    { id: 5, userId: 3, action: 'Flagged expiry issue', timestamp: '2025-05-08T14:30:00Z' },
    { id: 6, userId: 3, action: 'Extension request initiated', timestamp: '2025-05-08T16:20:00Z' }
  ]
};

// Message Modal Component
const MessageModal = ({ title, message, onClose, type = 'info' }) => {
  let bgColor, textColor, icon;
  switch (type) {
    case 'success':
      bgColor = 'from-emerald-500 to-green-600';
      textColor = 'text-green-800';
      icon = <CheckCircle className="w-8 h-8 text-white" />;
      break;
    case 'error':
      bgColor = 'from-red-500 to-pink-600';
      textColor = 'text-red-800';
      icon = <XCircle className="w-8 h-8 text-white" />;
      break;
    case 'warning':
      bgColor = 'from-amber-500 to-orange-600';
      textColor = 'text-orange-800';
      icon = <AlertCircle className="w-8 h-8 text-white" />;
      break;
    default: // info
      bgColor = 'from-blue-500 to-purple-600';
      textColor = 'text-blue-800';
      icon = <Bell className="w-8 h-8 text-white" />;
  }

  return (
    <div
      className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4"
      onClick={onClose}
    >
      <div
        className="backdrop-blur-xl bg-white/95 rounded-3xl shadow-2xl max-w-sm w-full border border-white/20"
        onClick={(e) => e.stopPropagation()}
      >
        <div className={`bg-gradient-to-r ${bgColor} text-white p-6 rounded-t-3xl flex items-center justify-between`}>
          <div className="flex items-center space-x-3">
            {icon}
            <h2 className="text-xl font-bold">{title}</h2>
          </div>
          <button onClick={onClose} className="text-white hover:text-white bg-white/20 hover:bg-white/30 p-2 rounded-xl transition-all duration-200">
            ✕
          </button>
        </div>
        <div className="p-6">
          <p className={`text-center ${textColor} font-medium`}>{message}</p>
          <button
            onClick={onClose}
            className="mt-6 w-full bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-3 rounded-2xl hover:from-blue-600 hover:to-purple-700 shadow-lg transform hover:scale-105 transition-all duration-200"
          >
            Got It
          </button>
        </div>
      </div>
    </div>
  );
};

const WithdrawalRequestTracker = () => {
  const [currentUser, setCurrentUser] = useState(null);
  const [selectedRequest, setSelectedRequest] = useState(null);
  const [showModal, setShowModal] = useState(false);
  const [showLoginModal, setShowLoginModal] = useState(false);
  const [actionToPerform, setActionToPerform] = useState(null);
  const [requests, setRequests] = useState(initialMockRequests);
  const [documents, setDocuments] = useState(initialMockDocuments);
  const [comments, setComments] = useState(initialMockComments);
  const [auditLog, setAuditLog] = useState(initialMockAuditLog);
  const [filterStatus, setFilterStatus] = useState('all');
  const [filterCountry, setFilterCountry] = useState('all');
  const [searchTerm, setSearchTerm] = useState('');
  const [showCreateRequest, setShowCreateRequest] = useState(false);
  const [newComment, setNewComment] = useState('');
  const [animateStats, setAnimateStats] = useState(false);
  const [isProcessingOCR, setIsProcessingOCR] = useState(false);
  const [ocrResults, setOcrResults] = useState(null);
  const [showOCRDemo, setShowOCRDemo] = useState(false);

  // State for Create Request form
  const [newRequestData, setNewRequestData] = useState({
    projectNumber: '',
    country: '',
    refNumber: '',
    beneficiaryName: '',
    amount: '',
    currency: 'USD',
    valueDate: '',
    priority: 'medium',
    documents: []
  });

  // State for Message Modal
  const [showMessageModal, setShowMessageModal] = useState(false);
  const [messageModalTitle, setMessageModalTitle] = useState('');
  const [messageModalMessage, setMessageModalMessage] = useState('');
  const [messageModalType, setMessageModalType] = useState('info');

  // Function to display custom message modal
  const showMessage = (title, message, type = 'info') => {
    setMessageModalTitle(title);
    setMessageModalMessage(message);
    setMessageModalType(type);
    setShowMessageModal(true);
  };

  // Effect for animating stats on load
  useEffect(() => {
    setAnimateStats(true);
    const timer = setTimeout(() => setAnimateStats(false), 1000);
    return () => clearTimeout(timer);
  }, []);

  // Effect for handling Escape key to close modals
  useEffect(() => {
    const handleEscKey = (event) => {
      if (event.key === 'Escape') {
        if (showMessageModal) setShowMessageModal(false);
        if (showModal) setShowModal(false);
        if (showLoginModal) setShowLoginModal(false);
        if (showCreateRequest) setShowCreateRequest(false);
      }
    };

    document.addEventListener('keydown', handleEscKey);
    return () => document.removeEventListener('keydown', handleEscKey);
  }, [showModal, showLoginModal, showCreateRequest, showMessageModal]);

  // Effect for preventing body scroll when modal is open
  useEffect(() => {
    if (showModal || showLoginModal || showCreateRequest || showMessageModal) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [showModal, showLoginModal, showCreateRequest, showMessageModal]);

  // Handles login attempt for an action
  const handleLoginForAction = (action, request = null) => {
    setActionToPerform({ action, request });
    setShowLoginModal(true);
  };

  // Handles actual login with selected username
  const handleLogin = (username) => {
    const user = initialMockUsers[username];
    setCurrentUser(user);
    setShowLoginModal(false);

    // Execute the pending action with strict role validation
    if (actionToPerform) {
      executeAction(actionToPerform.action, actionToPerform.request, user);
      setActionToPerform(null);
    }
  };

  // Executes a specific action after login and permission validation
  const executeAction = (action, request, user) => {
    // STRICT ROLE VALIDATION - No exceptions allowed
    const hasPermission = validateStrictPermission(action, request, user);

    if (!hasPermission) {
      showMessage(
        'Access Denied',
        `Only ${getRequiredRole(action)} can perform this action. Your role (${user.role.replace('_', ' ')}) is not authorized.`,
        'error'
      );
      return;
    }

    switch (action) {
      case 'create_request':
        setShowCreateRequest(true);
        break;
      case 'approve':
        // When operations approves, move to core banking and assign to core banking team
        const coreBankingUser = Object.values(initialMockUsers).find(user => user.role === 'core_banking');
        updateRequestStatus(request.id, 'Approved - Sent to Core Banking for disbursement', 'core_banking');
        // Update the assignment to core banking team
        setRequests(prev => prev.map(req =>
          req.id === request.id
            ? { ...req, assignedTo: coreBankingUser.id }
            : req
        ));
        addAuditEntry(request.id, user.id, `Request approved by ${user.name} and moved to Core Banking`);
        showMessage('Success', `Request #${request.projectNumber} approved and moved to Core Banking for disbursement.`, 'success');
        break;
      case 'reject':
        updateRequestStatus(request.id, 'Rejected by Operations', 'initial_review');
        addAuditEntry(request.id, user.id, 'Request rejected and sent back for review');
        showMessage('Success', `Request #${request.projectNumber} rejected and sent back to Initial Review.`, 'success');
        break;
      case 'disburse':
        updateRequestStatus(request.id, 'Disbursed', 'disbursed');
        addAuditEntry(request.id, user.id, `Disbursement processed: ${request.amount.toLocaleString()} ${request.currency}`);
        showMessage('Success', `Request #${request.projectNumber} marked as Disbursed.`, 'success');
        break;
      default:
        showMessage('Error', 'Invalid action requested.', 'error');
    }
  };

  // Strict permission validation based on role and current stage
  const validateStrictPermission = (action, request, user) => {
    console.log(`--- Permission Check for Action: ${action} ---`);
    console.log(`User: ${user?.name} (Role: ${user?.role})`);
    console.log(`Request ID: ${request?.id}, Current Stage: ${request?.currentStage}, Assigned To: ${request?.assignedTo}`);

    if (!user) {
      console.log('Permission Denied: No user logged in.');
      return false;
    }

    switch (action) {
      case 'create_request':
        const canCreate = user.role === 'archive';
        console.log(`Can Create? ${canCreate} (User role: ${user.role}, Required: archive)`);
        return canCreate;
      case 'approve':
      case 'reject':
        const isOperations = user.role === 'operations';
        const isTechnicalReview = request.currentStage === 'technical_review';
        const isAssignedToUser = request.assignedTo === user.id;
        const canApproveReject = isOperations && isTechnicalReview && isAssignedToUser;
        console.log(`Can Approve/Reject? ${canApproveReject}`);
        console.log(`  - Is Operations? ${isOperations}`);
        console.log(`  - Is Technical Review? ${isTechnicalReview}`);
        console.log(`  - Is Assigned to User? ${isAssignedToUser} (Assigned: ${request.assignedTo}, User ID: ${user.id})`);
        return canApproveReject;
      case 'disburse':
        const isCoreBanking = user.role === 'core_banking';
        const isCoreBankingStage = request.currentStage === 'core_banking';
        const canDisburse = isCoreBanking && isCoreBankingStage;
        console.log(`Can Disburse? ${canDisburse}`);
        console.log(`  - Is Core Banking Role? ${isCoreBanking}`);
        console.log(`  - Is Core Banking Stage? ${isCoreBankingStage}`);
        return canDisburse;
      default:
        console.log('Permission Denied: Unknown action.');
        return false;
    }
  };

  // Handles user logout
  const handleLogout = () => {
    setCurrentUser(null);
    showMessage('Logged Out', 'You have been successfully logged out.', 'info');
  };

  // Filters requests based on status, country, and search term
  const getFilteredRequests = () => {
    let filtered = requests;

    if (filterStatus !== 'all') {
      filtered = filtered.filter(req => req.currentStage === filterStatus);
    }

    if (filterCountry !== 'all') {
      filtered = filtered.filter(req => req.country === filterCountry);
    }

    if (searchTerm) {
      filtered = filtered.filter(req =>
        req.refNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        req.beneficiaryName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        req.projectNumber.includes(searchTerm)
      );
    }

    return filtered;
  };

  // Calculates dashboard statistics
  const getDashboardStats = () => {
    const allRequests = requests;
    const totalRequests = allRequests.length;
    const pendingRequests = allRequests.filter(req => req.currentStage !== 'disbursed').length;
    const avgProcessingTime = totalRequests > 0 ? Math.round(allRequests.reduce((acc, req) => acc + req.processingDays, 0) / totalRequests) : 0;
    const dueSoon = allRequests.filter(req => {
      const valueDate = new Date(req.valueDate);
      const today = new Date();
      const diffDays = Math.ceil((valueDate - today) / (1000 * 60 * 60 * 24));
      return diffDays <= 3 && req.currentStage !== 'disbursed';
    }).length;

    return { totalRequests, pendingRequests, avgProcessingTime, dueSoon };
  };

  // Returns appropriate icon based on request stage
  const getStatusIcon = (stage) => {
    switch (stage) {
      case 'disbursed': return <CheckCircle className="w-5 h-5 text-emerald-500" />;
      case 'core_banking': return <Clock className="w-5 h-5 text-blue-500" />;
      case 'technical_review': return <AlertCircle className="w-5 h-5 text-amber-500" />;
      case 'initial_review': return <Pause className="w-5 h-5 text-orange-500" />;
      default: return <XCircle className="w-5 h-5 text-red-500" />;
    }
  };

  // Returns Tailwind CSS classes for priority color
  const getPriorityColor = (priority) => {
    switch (priority) {
      case 'urgent': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'low': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Generates progress timeline stages
  const getStageProgress = (stage) => {
    const stages = ['initial_review', 'technical_review', 'core_banking', 'disbursed'];
    const currentIndex = stages.indexOf(stage);
    return stages.map((s, index) => ({
      name: s.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()),
      active: index <= currentIndex,
      current: index === currentIndex
    }));
  };

  // Updates a request's status and stage
  const updateRequestStatus = (requestId, newStatus, newStage) => {
    setRequests(prev => prev.map(req =>
      req.id === requestId
        ? { ...req, status: newStatus, currentStage: newStage, updatedAt: new Date().toISOString() }
        : req
    ));
  };

  // Adds a new comment to a request
  const addComment = (requestId) => {
    if (!newComment.trim() || !currentUser) {
      showMessage('Error', 'Comment cannot be empty or you must be logged in.', 'error');
      return;
    }

    const comment = {
      id: Date.now(),
      userId: currentUser.id,
      comment: newComment,
      createdAt: new Date().toISOString()
    };

    setComments(prevComments => ({
      ...prevComments,
      [requestId]: [...(prevComments[requestId] || []), comment]
    }));
    setNewComment('');
    addAuditEntry(requestId, currentUser.id, 'Comment added');
    showMessage('Comment Added', 'Your comment has been added successfully.', 'success');
  };

  // Adds an audit log entry
  const addAuditEntry = (requestId, userId, action) => {
    const entry = {
      id: Date.now(),
      userId,
      action,
      timestamp: new Date().toISOString()
    };

    setAuditLog(prev => ({
      ...prev,
      [requestId]: [...(prev[requestId] || []), entry]
    }));
  };

  // Returns the required role for a given action
  const getRequiredRole = (action) => {
    switch (action) {
      case 'create_request':
        return 'Archive Team';
      case 'approve':
      case 'reject':
        return 'Operations Team';
      case 'disburse':
        return 'Core Banking Team';
      default:
        return 'Authorized User';
    }
  };

  // Handles input changes for the new request form
  const handleNewRequestChange = (e) => {
    const { name, value } = e.target;
    setNewRequestData(prev => ({ ...prev, [name]: value }));
  };

  // Handles file upload for new request with OCR simulation
  const handleNewRequestDocumentUpload = (e) => {
    const files = Array.from(e.target.files);
    if (files.length > 0) {
      const file = files[0]; // Process first file for OCR demo

      // Start OCR processing simulation
      setIsProcessingOCR(true);
      setShowOCRDemo(true);

      // Simulate OCR processing delay
      setTimeout(() => {
        const extractedData = simulateOCRExtraction(file.name);
        setOcrResults(extractedData);

        // Auto-populate form fields with extracted data
        setNewRequestData(prev => ({
          ...prev,
          projectNumber: extractedData.projectNumber,
          country: extractedData.country,
          refNumber: extractedData.refNumber,
          beneficiaryName: extractedData.beneficiaryName,
          amount: extractedData.amount,
          currency: extractedData.currency,
          valueDate: extractedData.valueDate,
          priority: extractedData.priority,
          documents: [{
            id: Date.now(),
            filename: file.name,
            fileSize: `${(file.size / 1024 / 1024).toFixed(2)} MB`,
            uploadedAt: new Date().toISOString(),
            uploadedBy: currentUser.id
          }]
        }));

        setIsProcessingOCR(false);
        showMessage('OCR Complete', 'Form fields have been automatically populated from the withdrawal request document!', 'success');
      }, 3000); // 3 second simulation
    }
  };

  // Handles the creation of a new request
  const handleCreateRequest = () => {
    if (!currentUser || currentUser.role !== 'archive') {
      showMessage('Access Denied', 'Only Archive Team can create new requests.', 'error');
      return;
    }

    const { projectNumber, country, refNumber, beneficiaryName, amount, currency, valueDate, priority, documents: uploadedDocuments } = newRequestData;

    if (!projectNumber || !country || !refNumber || !beneficiaryName || !amount || !valueDate) {
      showMessage('Validation Error', 'Please fill in all required fields.', 'warning');
      return;
    }
    if (isNaN(parseFloat(amount))) {
      showMessage('Validation Error', 'Amount must be a valid number.', 'warning');
      return;
    }

    // Get regional assignment for operations team
    const region = regionMapping[country] || 'Unknown Region';
    const assignedOperationsTeam = getOperationsTeamByRegion(country);

    const newRequestId = Math.max(...requests.map(r => r.id)) + 1;
    const now = new Date().toISOString();

    const newRequest = {
      id: newRequestId,
      projectNumber,
      country,
      refNumber,
      beneficiaryName,
      amount: parseFloat(amount),
      currency,
      valueDate,
      status: `New Request - Assigned to ${assignedOperationsTeam.name} (${region})`,
      currentStage: 'initial_review',
      assignedTo: assignedOperationsTeam.id, // Auto-assign based on region
      createdBy: currentUser.id,
      createdAt: now,
      updatedAt: now,
      processingDays: 0,
      priority,
      region
    };

    setRequests(prev => [...prev, newRequest]);
    setDocuments(prev => ({
      ...prev,
      [newRequestId]: uploadedDocuments
    }));
    setComments(prev => ({
      ...prev,
      [newRequestId]: [{
        id: Date.now(),
        userId: currentUser.id,
        comment: `New request created with OCR extraction. Auto-assigned to ${assignedOperationsTeam.name} for ${region} region.`,
        createdAt: now
      }]
    }));
    setAuditLog(prev => ({
      ...prev,
      [newRequestId]: [
        { id: Date.now(), userId: currentUser.id, action: 'Request created via OCR extraction', timestamp: now },
        { id: Date.now() + 1, userId: currentUser.id, action: `Auto-assigned to ${assignedOperationsTeam.name} (${region})`, timestamp: now }
      ]
    }));

    setNewRequestData({
      projectNumber: '', country: '', refNumber: '', beneficiaryName: '', amount: '',
      currency: 'USD', valueDate: '', priority: 'medium', documents: []
    });
    setOcrResults(null);
    setShowOCRDemo(false);
    setShowCreateRequest(false);
    showMessage(
      'Request Created',
      `New request #${newRequest.projectNumber} created and auto-assigned to ${assignedOperationsTeam.name} for ${region} region!`,
      'success'
    );
  };

  const stats = getDashboardStats();
  const filteredRequests = getFilteredRequests();

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100 font-sans">
      {/* Animated Background */}
      <div className="fixed inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-96 h-96 bg-gradient-to-r from-blue-400/20 to-purple-500/20 rounded-full blur-3xl animate-pulse"></div>
        <div className="absolute -bottom-40 -left-40 w-96 h-96 bg-gradient-to-r from-pink-400/20 to-blue-500/20 rounded-full blur-3xl animate-pulse delay-1000"></div>
      </div>

      {/* Header */}
      <header className="relative z-10 backdrop-blur-xl bg-white/80 shadow-lg border-b border-white/20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-20">
            <div className="flex items-center space-x-4">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center shadow-lg">
                <DollarSign className="w-6 h-6 text-white" />
              </div>
              <div>
                <h1 className="text-2xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                  Withdrawal Request Tracker
                </h1>
                <p className="text-sm text-gray-600">AI-powered OCR • Regional auto-assignment • Real-time tracking • Strict role controls</p>
              </div>
            </div>

            <div className="flex items-center space-x-6">
              <button
                onClick={() => handleLoginForAction('create_request')}
                className="bg-gradient-to-r from-green-500 to-emerald-600 text-white px-6 py-3 rounded-2xl hover:from-green-600 hover:to-emerald-700 flex items-center space-x-2 shadow-lg transform hover:scale-105 transition-all duration-200"
              >
                <Plus className="w-5 h-5" />
                <span className="font-medium">New Request</span>
                <span className="text-xs bg-white/20 px-2 py-1 rounded-lg">Archive Only</span>
              </button>

              {currentUser ? (
                <div className="flex items-center space-x-3 bg-white/50 backdrop-blur-md rounded-2xl px-4 py-3 shadow-lg">
                  <div className="w-10 h-10 bg-gradient-to-r from-blue-400 to-purple-500 rounded-xl flex items-center justify-center text-lg">
                    {currentUser.avatar}
                  </div>
                  <div>
                    <div className="text-sm font-semibold text-gray-800">{currentUser.name}</div>
                    <div className="text-xs text-gray-600 capitalize">
                      {currentUser.role.replace('_', ' ')}
                      {currentUser.role === 'loan_admin' && <span className="text-blue-600 ml-1">(View Only)</span>}
                    </div>
                  </div>
                  <button onClick={handleLogout} className="p-2 text-gray-500 hover:text-gray-700 hover:bg-white/50 rounded-xl transition-all duration-200">
                    <LogOut className="w-4 h-4" />
                  </button>
                </div>
              ) : (
                <button
                  onClick={() => setShowLoginModal(true)}
                  className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-3 rounded-2xl hover:from-blue-600 hover:to-purple-700 flex items-center space-x-2 shadow-lg transform hover:scale-105 transition-all duration-200"
                >
                  <Shield className="w-5 h-5" />
                  <span className="font-medium">Login for Actions</span>
                </button>
              )}
            </div>
          </div>
        </div>
      </header>

      {/* Dashboard */}
      <main className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Urgent Actions Alert */}
        {requests.filter(r => r.currentStage === 'core_banking').length > 0 && (
          <div className="backdrop-blur-xl bg-gradient-to-r from-emerald-500 to-green-600 text-white p-6 rounded-3xl shadow-lg border border-white/20 mb-8 animate-pulse">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 bg-white/20 rounded-2xl flex items-center justify-center">
                  <DollarSign className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h3 className="text-xl font-bold">🏦 Disbursement Required</h3>
                  <p className="text-emerald-100">
                    {requests.filter(r => r.currentStage === 'core_banking').length} request(s) ready for Core Banking disbursement
                  </p>
                </div>
              </div>
              <div className="text-right">
                <div className="text-2xl font-bold">
                  {requests.filter(r => r.currentStage === 'core_banking')
                    .reduce((sum, r) => sum + r.amount, 0).toLocaleString()}
                </div>
                <div className="text-emerald-100 text-sm">Total Amount Ready</div>
              </div>
            </div>
          </div>
        )}

        {/* Regional Operations Overview */}
        <div className="backdrop-blur-xl bg-white/80 p-6 rounded-3xl shadow-lg border border-white/20 mb-8">
          <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
            <Users className="w-6 h-6 mr-2 text-blue-500" />
            Regional Operations Team Assignments
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            {[
              { region: 'North Africa', member: 'Ahmed Operations', avatar: '👨‍🔧', countries: ['Egypt', 'Libya', 'Tunisia', 'Algeria', 'Morocco', 'Sudan'], count: requests.filter(r => ['Egypt', 'Libya', 'Tunisia', 'Algeria', 'Morocco', 'Sudan'].includes(r.country)).length },
              { region: 'Central Africa', member: 'Fatima Operations', avatar: '👩‍🔧', countries: ['Chad', 'Central African Republic', 'Democratic Republic of Congo', 'Cameroon', 'Nigeria', 'Kenya'], count: requests.filter(r => ['Chad', 'Central African Republic', 'Democratic Republic of Congo', 'Cameroon', 'Nigeria', 'Kenya'].includes(r.country)).length },
              { region: 'South East Asia', member: 'Chen Operations', avatar: '👨‍💼', countries: ['Malaysia', 'Indonesia', 'Thailand', 'Vietnam', 'Philippines', 'Singapore'], count: requests.filter(r => ['Malaysia', 'Indonesia', 'Thailand', 'Vietnam', 'Philippines', 'Singapore'].includes(r.country)).length },
              { region: 'Central Asia', member: 'Amira Operations', avatar: '👩‍💼', countries: ['Kazakhstan', 'Uzbekistan', 'Kyrgyzstan', 'Tajikistan', 'Afghanistan', 'Pakistan'], count: requests.filter(r => ['Kazakhstan', 'Uzbekistan', 'Kyrgyzstan', 'Tajikistan', 'Afghanistan', 'Pakistan'].includes(r.country)).length }
            ].map((team) => (
              <div key={team.region} className="bg-gradient-to-br from-white to-gray-50 p-4 rounded-2xl border border-gray-200 hover:shadow-lg transition-all duration-200">
                <div className="flex items-center space-x-3 mb-3">
                  <div className="w-12 h-12 bg-gradient-to-r from-blue-400 to-purple-500 rounded-xl flex items-center justify-center text-xl">
                    {team.avatar}
                  </div>
                  <div>
                    <h3 className="font-bold text-gray-900">{team.member}</h3>
                    <p className="text-sm text-blue-600">{team.region}</p>
                  </div>
                </div>
                <div className="text-center mb-3">
                  <div className="text-2xl font-bold text-gray-900">{team.count}</div>
                  <div className="text-sm text-gray-600">Active Requests</div>
                </div>
                <div className="text-xs text-gray-500">
                  {team.countries.slice(0, 3).join(', ')}
                  {team.countries.length > 3 && ` +${team.countries.length - 3} more`}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Real-time Process Overview */}
        <div className="backdrop-blur-xl bg-white/80 p-6 rounded-3xl shadow-lg border border-white/20 mb-8">
          <h2 className="text-xl font-bold text-gray-900 mb-4 flex items-center">
            <Activity className="w-6 h-6 mr-2 text-blue-500" />
            Live Process Tracking
          </h2>
          <div className="grid grid-cols-4 gap-6">
            {[
              { stage: 'initial_review', name: 'Initial Review', count: requests.filter(r => r.currentStage === 'initial_review').length, color: 'from-orange-500 to-red-500' },
              { stage: 'technical_review', name: 'Technical Review', count: requests.filter(r => r.currentStage === 'technical_review').length, color: 'from-amber-500 to-orange-500' },
              { stage: 'core_banking', name: 'Core Banking', count: requests.filter(r => r.currentStage === 'core_banking').length, color: 'from-blue-500 to-indigo-500' },
              { stage: 'disbursed', name: 'Disbursed', count: requests.filter(r => r.currentStage === 'disbursed').length, color: 'from-emerald-500 to-green-500' }
            ].map((stage) => (
              <div key={stage.stage} className="text-center">
                <div className={`w-16 h-16 bg-gradient-to-r ${stage.color} rounded-3xl flex items-center justify-center mx-auto mb-3 shadow-lg`}>
                  <span className="text-2xl font-bold text-white">{stage.count}</span>
                </div>
                <h3 className="font-semibold text-gray-900">{stage.name}</h3>
                <p className="text-sm text-gray-600">Active requests</p>
              </div>
            ))}
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          {[
            { icon: DollarSign, label: 'Total Requests', value: stats.totalRequests, color: 'from-blue-500 to-blue-600', desc: 'All time' },
            { icon: Clock, label: 'Pending Review', value: stats.pendingRequests, color: 'from-amber-500 to-orange-500', desc: 'Awaiting action' },
            { icon: TrendingUp, label: 'Avg Processing', value: `${stats.avgProcessingTime} days`, color: 'from-emerald-500 to-green-600', desc: 'Current efficiency' },
            { icon: AlertCircle, label: 'Due Soon', value: stats.dueSoon, color: 'from-red-500 to-pink-500', desc: 'Urgent attention' }
          ].map((stat) => (
            <div
              key={stat.label}
              className={`backdrop-blur-xl bg-white/80 p-6 rounded-3xl shadow-lg border border-white/20 hover:shadow-xl transform hover:scale-105 transition-all duration-300 ${animateStats ? 'animate-pulse' : ''}`}
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600 mb-1">{stat.label}</p>
                  <p className="text-3xl font-bold text-gray-900 mb-1">{stat.value}</p>
                  <p className="text-xs text-gray-500">{stat.desc}</p>
                </div>
                <div className={`w-12 h-12 bg-gradient-to-r ${stat.color} rounded-2xl flex items-center justify-center shadow-lg`}>
                  <stat.icon className="w-6 h-6 text-white" />
                </div>
              </div>
              <div className="mt-4 h-1 bg-gray-200 rounded-full overflow-hidden">
                <div className={`h-full bg-gradient-to-r ${stat.color}`} style={{ width: '100%' }}></div>
              </div>
            </div>
          ))}
        </div>

        {/* Filters */}
        <div className="backdrop-blur-xl bg-white/80 p-6 rounded-3xl shadow-lg border border-white/20 mb-8">
          <div className="flex flex-wrap items-center gap-4">
            <div className="flex items-center space-x-3 bg-white/50 rounded-2xl px-4 py-3">
              <Search className="w-5 h-5 text-gray-500" />
              <input
                type="text"
                placeholder="Search by reference, beneficiary, or project..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="bg-transparent border-none outline-none w-72 text-gray-700 placeholder-gray-500"
              />
            </div>

            <select
              value={filterStatus}
              onChange={(e) => setFilterStatus(e.target.value)}
              className="bg-white/50 border border-white/20 rounded-2xl px-4 py-3 text-gray-700 backdrop-blur-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Status</option>
              <option value="initial_review">Initial Review</option>
              <option value="technical_review">Technical Review</option>
              <option value="core_banking">Core Banking</option>
              <option value="disbursed">Disbursed</option>
            </select>

            <select
              value={filterCountry}
              onChange={(e) => setFilterCountry(e.target.value)}
              className="bg-white/50 border border-white/20 rounded-2xl px-4 py-3 text-gray-700 backdrop-blur-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="all">All Countries</option>
              <optgroup label="🌍 North Africa">
                {Object.keys(regionMapping).filter(country => regionMapping[country] === 'North Africa').map(country => (
                  <option key={country} value={country}>{country}</option>
                ))}
              </optgroup>
              <optgroup label="🌍 Central Africa">
                {Object.keys(regionMapping).filter(country => regionMapping[country] === 'Central Africa').map(country => (
                  <option key={country} value={country}>{country}</option>
                ))}
              </optgroup>
              <optgroup label="🌏 South East Asia">
                {Object.keys(regionMapping).filter(country => regionMapping[country] === 'South East Asia').map(country => (
                  <option key={country} value={country}>{country}</option>
                ))}
              </optgroup>
              <optgroup label="🌏 Central Asia">
                {Object.keys(regionMapping).filter(country => regionMapping[country] === 'Central Asia').map(country => (
                  <option key={country} value={country}>{country}</option>
                ))}
              </optgroup>
            </select>

            <div className="ml-auto bg-green-50 border border-green-200 rounded-2xl px-4 py-3">
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-green-800">Live Tracking Active</span>
              </div>
            </div>
          </div>
        </div>

        {/* Requests Table */}
        <div className="backdrop-blur-xl bg-white/80 rounded-3xl shadow-xl border border-white/20 overflow-hidden">
          <div className="bg-gradient-to-r from-gray-50/80 to-gray-100/80 backdrop-blur-md px-6 py-4">
            <h3 className="font-bold text-gray-900 flex items-center">
              <Users className="w-5 h-5 mr-2 text-blue-500" />
              All Withdrawal Requests - Public Tracking Dashboard
            </h3>
            <p className="text-sm text-gray-600 mt-1">
              🔍 OCR-powered extraction • 🌍 Regional auto-assignment • 🔒 Strict role controls: Archive (create) • Operations (regional approval) • Core Banking (disburse)
            </p>
          </div>

          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead className="bg-gradient-to-r from-gray-50/80 to-gray-100/80 backdrop-blur-md">
                <tr>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Value Date</th>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Project Details</th>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Amount</th>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Current Status</th>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">Processing Time</th>
                  <th className="px-6 py-4 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider">View & Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-100/50">
                {filteredRequests.map((request) => (
                  <tr
                    key={request.id}
                    className="hover:bg-white/60 transition-all duration-200 group"
                  >
                    <td className="px-6 py-6 whitespace-nowrap">
                      <div className="flex items-center">
                        <Calendar className="w-4 h-4 text-gray-400 mr-2" />
                        <span className="text-sm font-medium text-gray-900">
                          {new Date(request.valueDate).toLocaleDateString()}
                        </span>
                      </div>
                    </td>
                    <td className="px-6 py-6">
                      <div className="space-y-1">
                        <div className="flex items-center space-x-2">
                          <span className="text-sm font-bold text-gray-900">#{request.projectNumber}</span>
                          <span className={`px-2 py-1 text-xs font-medium rounded-lg border ${getPriorityColor(request.priority)}`}>
                            {request.priority}
                          </span>
                        </div>
                        <div className="text-sm text-gray-600">{request.country}</div>
                        <div className="text-xs text-blue-500 bg-blue-50 px-2 py-1 rounded-lg inline-block">
                          {regionMapping[request.country] || 'Unknown Region'}
                        </div>
                        <div className="text-sm font-medium text-blue-600">{request.refNumber}</div>
                        <div className="text-sm text-gray-500">{request.beneficiaryName}</div>
                      </div>
                    </td>
                    <td className="px-6 py-6 whitespace-nowrap">
                      <div className="text-lg font-bold text-gray-900">
                        {request.amount.toLocaleString()} {request.currency}
                      </div>
                    </td>
                    <td className="px-6 py-6">
                      <div className="flex items-center space-x-3 mb-2">
                        {getStatusIcon(request.currentStage)}
                        <span className="text-sm font-medium text-gray-900 capitalize">
                          {request.currentStage.replace('_', ' ')}
                        </span>
                      </div>
                      <div className="text-xs text-gray-500 bg-gray-50 rounded-lg p-2 max-w-xs">
                        {request.status.length > 60 ? `${request.status.substring(0, 60)}...` : request.status}
                      </div>
                    </td>
                    <td className="px-6 py-6 whitespace-nowrap">
                      <div className="flex items-center space-x-2">
                        <Clock className="w-4 h-4 text-gray-400" />
                        <span className="text-sm font-medium text-gray-900">{request.processingDays} days</span>
                      </div>
                    </td>
                    <td className="px-6 py-6 whitespace-nowrap">
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => {
                            setSelectedRequest(request);
                            setShowModal(true);
                          }}
                          className="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-3 rounded-2xl hover:from-blue-600 hover:to-purple-700 transform hover:scale-110 transition-all duration-200 shadow-lg group-hover:shadow-xl"
                          title="View Details & Actions"
                        >
                          <Eye className="w-5 h-5" />
                        </button>

                        {/* Enhanced Action Buttons with Better UX */}
                        {request.currentStage === 'technical_review' && (
                          <>
                            <button
                              onClick={() => handleLoginForAction('approve', request)}
                              className="bg-gradient-to-r from-emerald-500 to-green-600 text-white p-3 rounded-2xl hover:from-emerald-600 hover:to-green-700 transform hover:scale-110 transition-all duration-200 shadow-lg relative group"
                              title="Approve (Operations Team Only)"
                            >
                              <CheckCircle className="w-5 h-5" />
                              <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                                Operations Only
                              </div>
                            </button>

                            <button
                              onClick={() => handleLoginForAction('reject', request)}
                              className="bg-gradient-to-r from-red-500 to-pink-500 text-white p-3 rounded-2xl hover:from-red-600 hover:to-pink-600 transform hover:scale-110 transition-all duration-200 shadow-lg relative group"
                              title="Reject (Operations Team Only)"
                            >
                              <XCircle className="w-5 h-5" />
                              <div className="absolute -top-8 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs px-2 py-1 rounded opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                                Operations Only
                              </div>
                            </button>
                          </>
                        )}

                        {request.currentStage === 'core_banking' && (
                          <div className="relative">
                            <button
                              onClick={() => {
                                console.log('Disbursement button clicked for request:', request.id, 'Stage:', request.currentStage);
                                handleLoginForAction('disburse', request);
                              }}
                              className="bg-gradient-to-r from-emerald-500 to-green-600 text-white p-3 rounded-2xl hover:from-emerald-600 hover:to-green-700 transform hover:scale-110 transition-all duration-200 shadow-lg relative group animate-pulse"
                              title="Process Disbursement (Core Banking Team Only)"
                            >
                              <DollarSign className="w-5 h-5" />
                              <div className="absolute -top-12 left-1/2 transform -translate-x-1/2 bg-emerald-800 text-white text-xs px-3 py-2 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap z-10">
                                <div className="font-bold">Ready to Disburse</div>
                                <div>{request.amount.toLocaleString()} {request.currency}</div>
                              </div>
                            </button>
                            {/* Pulsing indicator for urgent disbursement */}
                            <div className="absolute -top-1 -right-1 w-3 h-3 bg-emerald-400 rounded-full animate-ping"></div>
                            <div className="absolute -top-1 -right-1 w-3 h-3 bg-emerald-500 rounded-full"></div>
                          </div>
                        )}

                        {request.currentStage === 'disbursed' && (
                          <div className="bg-emerald-100 text-emerald-700 p-3 rounded-2xl">
                            <CheckCircle className="w-5 h-5" />
                          </div>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </main>

      {/* Role Selection Modal */}
      {showLoginModal && (
        <div
          className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4"
          onClick={(e) => {
            if (e.target === e.currentTarget) {
              setShowLoginModal(false);
            }
          }}
        >
          <div
            className="backdrop-blur-xl bg-white/95 rounded-3xl shadow-2xl max-w-md w-full border border-white/20"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-6 rounded-t-3xl">
              <div className="flex justify-between items-center">
                <div>
                  <h2 className="text-xl font-bold">
                    {actionToPerform?.action === 'disburse' ? '🏦 Disbursement Authorization Required' : '🔒 Strict Role Authentication'}
                  </h2>
                  <p className="text-blue-100 text-sm">
                    {actionToPerform && actionToPerform.action === 'disburse'
                      ? `Amount: ${actionToPerform.request?.amount?.toLocaleString()} ${actionToPerform.request?.currency} - Core Banking Team Only`
                      : actionToPerform && `Required Role: ${getRequiredRole(actionToPerform.action)}`
                    }
                  </p>
                </div>
                <button
                  onClick={() => setShowLoginModal(false)}
                  className="text-white hover:text-white bg-white/20 hover:bg-white/30 p-2 rounded-xl transition-all duration-200"
                >
                  ✕
                </button>
              </div>
            </div>

            <div className="p-6">
              {/* Debug: Show current action info */}
              {actionToPerform && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-2xl p-3 mb-4">
                  <div className="text-sm text-yellow-800">
                    <strong>Debug Info:</strong> Action: {actionToPerform.action} |
                    Stage: {actionToPerform.request?.currentStage} |
                    Assigned To: {initialMockUsers[Object.keys(initialMockUsers).find(k => initialMockUsers[k].id === actionToPerform.request?.assignedTo)]?.name || 'N/A'}
                  </div>
                </div>
              )}

              <div className="bg-red-50 border border-red-200 rounded-2xl p-4 mb-6">
                <h3 className="font-bold text-red-800 mb-2">⚠️ Strict Access Control Rules</h3>
                <div className="text-sm text-red-700 space-y-1">
                  <div>🏛️ <strong>Archive Team:</strong> Can ONLY create new requests</div>
                  <div>👀 <strong>Loan Administrator:</strong> View-only access (no actions)</div>
                  <div>⚖️ <strong>Operations Teams:</strong> Can ONLY approve/reject requests in their region</div>
                  <div>🏦 <strong>Core Banking Team:</strong> Can ONLY process disbursements</div>
                </div>
              </div>

              <div className="space-y-3">
                {Object.entries(initialMockUsers).map(([username, user]) => {
                  const canPerformCurrentAction = actionToPerform ? validateStrictPermission(actionToPerform.action, actionToPerform.request, user) : true;

                  return (
                    <button
                      key={username}
                      onClick={() => handleLogin(username)}
                      disabled={!canPerformCurrentAction}
                      className={`w-full p-4 text-left rounded-2xl border transition-all duration-300 group ${
                        canPerformCurrentAction
                          ? 'bg-white/50 hover:bg-white/70 border-white/20 hover:transform hover:scale-105'
                          : 'bg-gray-100 border-gray-200 opacity-50 cursor-not-allowed'
                      }`}
                    >
                      <div className="flex items-center space-x-4">
                        <div className={`w-12 h-12 rounded-xl flex items-center justify-center text-xl ${
                          canPerformCurrentAction
                            ? 'bg-gradient-to-r from-blue-400 to-purple-500'
                            : 'bg-gray-300'
                        }`}>
                          {user.avatar}
                        </div>
                        <div className="flex-1">
                          <div className={`font-semibold ${canPerformCurrentAction ? 'text-gray-900' : 'text-gray-500'}`}>
                            {user.name}
                          </div>
                          <div className={`text-sm capitalize ${canPerformCurrentAction ? 'text-gray-600' : 'text-gray-400'}`}>
                            {user.role.replace('_', ' ')}
                            {user.role === 'loan_admin' && ' (View Only)'}
                            {user.role === 'operations' && user.region && ` - ${user.region}`}
                          </div>
                          {!canPerformCurrentAction && actionToPerform && (
                            <div className="text-xs text-red-600 font-medium mt-1">
                              ❌ Not authorized for this action
                            </div>
                          )}
                          {canPerformCurrentAction && actionToPerform && user.role === 'core_banking' && (
                            <div className="text-xs text-green-600 font-medium mt-1">
                              ✅ Can process disbursements
                            </div>
                          )}
                        </div>
                        {canPerformCurrentAction ? (
                          <ArrowRight className="w-5 h-5 text-gray-400 group-hover:text-gray-600 group-hover:translate-x-1 transition-all" />
                        ) : (
                          <Lock className="w-5 h-5 text-gray-400" />
                        )}
                      </div>
                    </button>
                  );
                })}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Request Detail Modal */}
      {showModal && selectedRequest && (
        <div
          className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-start justify-center z-50 p-4 overflow-y-auto"
          onClick={(e) => {
            if (e.target === e.currentTarget) {
              setShowModal(false);
            }
          }}
        >
          <div
            className="backdrop-blur-xl bg-white/95 rounded-3xl shadow-2xl max-w-5xl w-full my-8 border border-white/20"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="sticky top-0 bg-gradient-to-r from-blue-500 to-purple-600 text-white p-6 rounded-t-3xl z-10 shadow-lg">
              <div className="flex justify-between items-center">
                <div>
                  <h2 className="text-2xl font-bold">Request Details</h2>
                  <p className="text-blue-100">{selectedRequest.refNumber} • Public Tracking View</p>
                </div>
                <button
                  onClick={() => setShowModal(false)}
                  className="text-white hover:text-white bg-white/20 hover:bg-white/30 p-3 rounded-2xl transition-all duration-200 flex items-center justify-center"
                  title="Close"
                >
                  <span className="text-xl font-bold">✕</span>
                </button>
              </div>
            </div>

            <div className="max-h-[80vh] overflow-y-auto">
              <div className="p-8 space-y-8">
                {/* Project Info */}
                <div className="backdrop-blur-md bg-white/50 p-6 rounded-3xl border border-white/20">
                  <h3 className="font-bold text-gray-900 mb-4 flex items-center">
                    <FileText className="w-5 h-5 mr-2 text-blue-500" />
                    Project Information
                  </h3>
                  <div className="grid grid-cols-2 gap-6">
                    {[
                      { label: 'Project Number', value: selectedRequest.projectNumber },
                      { label: 'Country', value: selectedRequest.country },
                      { label: 'Beneficiary', value: selectedRequest.beneficiaryName },
                      { label: 'Amount', value: `${selectedRequest.amount.toLocaleString()} ${selectedRequest.currency}` },
                      { label: 'Value Date', value: new Date(selectedRequest.valueDate).toLocaleDateString() },
                      { label: 'Processing Time', value: `${selectedRequest.processingDays} days` }
                    ].map((item) => (
                      <div key={item.label} className="space-y-1">
                        <span className="text-sm text-gray-600 font-medium">{item.label}</span>
                        <div className="text-lg font-semibold text-gray-900">{item.value}</div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Progress Timeline */}
                <div className="backdrop-blur-md bg-white/50 p-6 rounded-3xl border border-white/20">
                  <h3 className="font-bold text-gray-900 mb-6 flex items-center">
                    <Activity className="w-5 h-5 mr-2 text-blue-500" />
                    Progress Timeline
                  </h3>
                  <div className="relative">
                    <div className="flex items-center justify-between mb-6">
                      {getStageProgress(selectedRequest.currentStage).map((stage, index) => (
                        <div key={stage.name} className="flex flex-col items-center flex-1">
                          <div className={`w-12 h-12 rounded-2xl flex items-center justify-center transition-all duration-500 ${
                            stage.active
                              ? 'bg-gradient-to-r from-blue-500 to-purple-600 text-white shadow-lg transform scale-110'
                              : 'bg-gray-200 text-gray-500'
                          }`}>
                            {stage.active ? '✓' : index + 1}
                          </div>
                          <div className="mt-3 text-center">
                            <div className={`text-sm font-medium ${stage.active ? 'text-blue-600' : 'text-gray-500'}`}>
                              {stage.name}
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                    <div className="bg-white/70 rounded-2xl p-4 space-y-2">
                      <div className="text-sm text-gray-600">
                        <strong>Current Status:</strong> {selectedRequest.status}
                      </div>
                      <div className="text-sm text-gray-600">
                        <strong>Assigned To:</strong> {initialMockUsers[Object.keys(initialMockUsers).find(k => initialMockUsers[k].id === selectedRequest.assignedTo)]?.name || 'Unassigned'}
                      </div>
                    </div>
                  </div>
                </div>

                {/* Action Center - Role-based Actions */}
                <div className="backdrop-blur-md bg-white/50 p-6 rounded-3xl border border-white/20">
                  <h3 className="font-bold text-gray-900 mb-4 flex items-center">
                    <Shield className="w-5 h-5 mr-2 text-blue-500" />
                    Action Center - Current Stage: {selectedRequest.currentStage.replace('_', ' ').toUpperCase()}
                  </h3>

                  {/* Current Stage Info */}
                  <div className="bg-gradient-to-r from-gray-50 to-gray-100 rounded-2xl p-4 mb-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <span className="text-sm text-gray-600 font-medium">Current Status</span>
                        <div className="text-lg font-semibold text-gray-900 mt-1">{selectedRequest.status}</div>
                      </div>
                      <div>
                        <span className="text-sm text-gray-600 font-medium">Assigned To</span>
                        <div className="text-lg font-semibold text-gray-900 mt-1">
                          {initialMockUsers[Object.keys(initialMockUsers).find(k => initialMockUsers[k].id === selectedRequest.assignedTo)]?.name || 'Unassigned'}
                          <span className="text-sm text-gray-500 ml-2">
                            ({initialMockUsers[Object.keys(initialMockUsers).find(k => initialMockUsers[k].id === selectedRequest.assignedTo)]?.role.replace('_', ' ') || 'N/A'})
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Available Actions Based on Stage */}
                  {selectedRequest.currentStage === 'initial_review' && (
                    <div className="space-y-4">
                      <div className="bg-orange-50 border border-orange-200 rounded-2xl p-4">
                        <h4 className="font-bold text-orange-800 mb-2">📋 Initial Review Stage</h4>
                        <p className="text-orange-700 text-sm mb-3">Request is being reviewed for completeness and documentation.</p>
                        <div className="text-sm text-orange-600">
                          <strong>Next Actions:</strong> Loan Admin will review and move to Technical Review when ready.
                        </div>
                      </div>
                    </div>
                  )}

                  {selectedRequest.currentStage === 'technical_review' && (
                    <div className="space-y-4">
                      <div className="bg-amber-50 border border-amber-200 rounded-2xl p-4">
                        <h4 className="font-bold text-amber-800 mb-2">⚖️ Technical Review Stage</h4>
                        <p className="text-amber-700 text-sm mb-3">Operations team needs to review technical requirements and make approval decision.</p>
                        <div className="text-sm text-amber-600 mb-4">
                          <strong>Required Action:</strong> Operations Team must approve or reject this request.
                        </div>
                      </div>

                      {currentUser && currentUser.role === 'operations' ? (
                        <div className="flex space-x-4">
                          <button
                            onClick={() => executeAction('approve', selectedRequest, currentUser)}
                            className="flex-1 bg-gradient-to-r from-emerald-500 to-green-600 text-white p-4 rounded-2xl hover:from-emerald-600 hover:to-green-700 transform hover:scale-105 transition-all duration-200 shadow-lg flex items-center justify-center space-x-2"
                          >
                            <CheckCircle className="w-5 h-5" />
                            <span className="font-semibold">Approve Request</span>
                          </button>
                          <button
                            onClick={() => executeAction('reject', selectedRequest, currentUser)}
                            className="flex-1 bg-gradient-to-r from-red-500 to-pink-500 text-white p-4 rounded-2xl hover:from-red-600 hover:to-pink-600 transform hover:scale-105 transition-all duration-200 shadow-lg flex items-center justify-center space-x-2"
                          >
                            <XCircle className="w-5 h-5" />
                            <span className="font-semibold">Reject Request</span>
                          </button>
                        </div>
                      ) : (
                        <div className="bg-red-50 border border-red-200 rounded-2xl p-4 text-center">
                          <Lock className="w-8 h-8 text-red-500 mx-auto mb-2" />
                          <p className="text-red-800 font-medium">Operations Team Access Required</p>
                          <p className="text-red-600 text-sm mt-1">
                            {currentUser
                              ? `Your role: ${currentUser.role.replace('_', ' ')} - Not authorized for approval actions`
                              : 'Login as Operations Team member to approve or reject this request'
                            }
                          </p>
                          {!currentUser && (
                            <button
                              onClick={() => {
                                setShowModal(false);
                                handleLoginForAction('approve', selectedRequest);
                              }}
                              className="mt-3 bg-red-600 text-white px-4 py-2 rounded-xl hover:bg-red-700 transition-all duration-200"
                            >
                              Login as Operations
                            </button>
                          )}
                        </div>
                      )}
                    </div>
                  )}

                  {selectedRequest.currentStage === 'core_banking' && (
                    <div className="space-y-4">
                      <div className="bg-blue-50 border border-blue-200 rounded-2xl p-4">
                        <h4 className="font-bold text-blue-800 mb-2">🏦 Core Banking Stage</h4>
                        <p className="text-blue-700 text-sm mb-3">Request approved and ready for disbursement processing.</p>
                        <div className="text-sm text-blue-600 mb-4">
                          <strong>Required Action:</strong> Core Banking Team must process the disbursement.
                        </div>
                        <div className="bg-blue-100 rounded-xl p-3 text-sm text-blue-800">
                          <strong>Amount to Disburse:</strong> {selectedRequest.amount.toLocaleString()} {selectedRequest.currency}
                        </div>
                      </div>

                      {currentUser && currentUser.role === 'core_banking' ? (
                        <div className="space-y-4">
                          <div className="bg-gradient-to-r from-emerald-50 to-green-50 border border-emerald-200 rounded-2xl p-4">
                            <h5 className="font-bold text-emerald-800 mb-2">💰 Ready for Disbursement</h5>
                            <div className="grid grid-cols-2 gap-4 text-sm text-emerald-700 mb-4">
                              <div><strong>Beneficiary:</strong> {selectedRequest.beneficiaryName}</div>
                              <div><strong>Amount:</strong> {selectedRequest.amount.toLocaleString()} {selectedRequest.currency}</div>
                              <div><strong>Value Date:</strong> {new Date(selectedRequest.valueDate).toLocaleDateString()}</div>
                              <div><strong>Reference:</strong> {selectedRequest.refNumber}</div>
                            </div>
                          </div>

                          <button
                            onClick={() => executeAction('disburse', selectedRequest, currentUser)}
                            className="w-full bg-gradient-to-r from-emerald-500 to-green-600 text-white p-6 rounded-2xl hover:from-emerald-600 hover:to-green-700 transform hover:scale-105 transition-all duration-200 shadow-lg flex items-center justify-center space-x-3"
                          >
                            <DollarSign className="w-6 h-6" />
                            <div className="text-left">
                              <div className="font-bold text-lg">Process Disbursement</div>
                              <div className="text-emerald-100 text-sm">Mark as disbursed and complete the request</div>
                            </div>
                          </button>
                        </div>
                      ) : (
                        <div className="bg-red-50 border border-red-200 rounded-2xl p-4 text-center">
                          <Lock className="w-8 h-8 text-red-500 mx-auto mb-2" />
                          <p className="text-red-800 font-medium">Core Banking Team Access Required</p>
                          <p className="text-red-600 text-sm mt-1">
                            {currentUser
                              ? `Your role: ${currentUser.role.replace('_', ' ')} - Not authorized for disbursement actions`
                              : 'Login as Core Banking Team member to process disbursement'
                            }
                          </p>
                          {!currentUser && (
                            <button
                              onClick={() => {
                                setShowModal(false);
                                handleLoginForAction('disburse', selectedRequest);
                              }}
                              className="mt-3 bg-red-600 text-white px-4 py-2 rounded-xl hover:bg-red-700 transition-all duration-200"
                            >
                              Login as Core Banking
                            </button>
                          )}
                        </div>
                      )}
                    </div>
                  )}

                  {selectedRequest.currentStage === 'disbursed' && (
                    <div className="space-y-4">
                      <div className="bg-emerald-50 border border-emerald-200 rounded-2xl p-4 text-center">
                        <div className="w-16 h-16 bg-emerald-100 rounded-full flex items-center justify-center mx-auto mb-4">
                          <CheckCircle className="w-8 h-8 text-emerald-500" />
                        </div>
                        <h4 className="font-bold text-emerald-800 mb-2">✅ Request Completed</h4>
                        <p className="text-emerald-700 text-sm mb-3">Disbursement has been processed successfully.</p>
                        <div className="bg-emerald-100 rounded-xl p-3 text-sm text-emerald-800">
                          <strong>Amount Disbursed:</strong> {selectedRequest.amount.toLocaleString()} {selectedRequest.currency}
                        </div>
                      </div>
                    </div>
                  )}
                </div>

                {/* Documents */}
                <div className="backdrop-blur-md bg-white/50 p-6 rounded-3xl border border-white/20">
                  <h3 className="font-bold text-gray-900 mb-4 flex items-center">
                    <FileText className="w-5 h-5 mr-2 text-blue-500" />
                    Documents
                  </h3>
                  {documents[selectedRequest.id] && documents[selectedRequest.id].length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {documents[selectedRequest.id].map(doc => (
                        <div key={doc.id} className="bg-white/70 p-4 rounded-2xl border border-white/20 hover:bg-white/90 transition-all duration-200 group">
                          <div className="flex items-center space-x-3">
                            <div className="w-12 h-12 bg-gradient-to-r from-blue-400 to-purple-500 rounded-2xl flex items-center justify-center">
                              <FileText className="w-6 h-6 text-white" />
                            </div>
                            <div className="flex-1">
                              <div className="text-sm font-semibold text-gray-900">{doc.filename}</div>
                              <div className="text-xs text-gray-500">{doc.fileSize}</div>
                            </div>
                            <button className="text-blue-600 hover:text-blue-800 p-2 hover:bg-blue-50 rounded-xl transition-all duration-200 group-hover:scale-110">
                              <Download className="w-5 h-5" />
                            </button>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <FileText className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                      <p>No documents uploaded</p>
                    </div>
                  )}
                </div>

                {/* Comments */}
                <div className="backdrop-blur-md bg-white/50 p-6 rounded-3xl border border-white/20">
                  <h3 className="font-bold text-gray-900 mb-4 flex items-center">
                    <MessageCircle className="w-5 h-5 mr-2 text-blue-500" />
                    Comments & Activity
                  </h3>
                  {comments[selectedRequest.id] && comments[selectedRequest.id].length > 0 ? (
                    <div className="space-y-4 mb-6">
                      {comments[selectedRequest.id].map(comment => (
                        <div key={comment.id} className="bg-white/70 p-4 rounded-2xl border border-white/20">
                          <div className="flex items-center justify-between mb-2">
                            <span className="text-sm font-semibold text-gray-900">
                              {initialMockUsers[Object.keys(initialMockUsers).find(k => initialMockUsers[k].id === comment.userId)]?.name}
                            </span>
                            <span className="text-xs text-gray-500">
                              {new Date(comment.createdAt).toLocaleString()}
                            </span>
                          </div>
                          <div className="text-sm text-gray-700">{comment.comment}</div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-gray-500 mb-6">
                      <MessageCircle className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                      <p>No comments yet</p>
                    </div>
                  )}

                  {currentUser ? (
                    <div className="space-y-4">
                      {/* Role-based comment permissions info */}
                      <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-2xl p-4">
                        <div className="flex items-center space-x-2 mb-2">
                          <Shield className="w-5 h-5 text-blue-600" />
                          <span className="font-semibold text-blue-800">Comment Permissions</span>
                        </div>
                        <div className="text-sm text-blue-700 space-y-1">
                          <div>✅ <strong>All authenticated users</strong> can add comments to any request</div>
                          <div>📝 Comments help track communication and decisions across departments</div>
                          <div>🔍 Your role: <strong>{currentUser.role.replace('_', ' ')}</strong> - You can comment on this request</div>
                        </div>
                      </div>

                      <div className="flex space-x-3">
                        <input
                          type="text"
                          value={newComment}
                          onChange={(e) => setNewComment(e.target.value)}
                          placeholder={`Add a comment as ${currentUser.name} (${currentUser.role.replace('_', ' ')})...`}
                          className="flex-1 bg-white/70 border border-white/20 rounded-2xl px-4 py-3 text-gray-700 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 backdrop-blur-md"
                          onKeyPress={(e) => e.key === 'Enter' && addComment(selectedRequest.id)}
                        />
                        <button
                          onClick={() => addComment(selectedRequest.id)}
                          className="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-3 rounded-2xl hover:from-blue-600 hover:to-purple-700 shadow-lg transform hover:scale-105 transition-all duration-200"
                          title="Add Comment"
                        >
                          <MessageCircle className="w-5 h-5" />
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-2xl p-6 text-center">
                      <Lock className="w-12 h-12 text-blue-500 mx-auto mb-4" />
                      <h4 className="text-lg font-bold text-blue-800 mb-2">Login Required for Comments</h4>
                      <div className="text-sm text-blue-700 mb-4 space-y-1">
                        <div>🏛️ <strong>Archive Team:</strong> Can comment on all requests</div>
                        <div>👀 <strong>Loan Admin:</strong> Can comment on all requests</div>
                        <div>⚖️ <strong>Operations Team:</strong> Can comment on all requests</div>
                        <div>🏦 <strong>Core Banking:</strong> Can comment on all requests</div>
                      </div>
                      <button
                        onClick={() => {
                          setShowModal(false);
                          setShowLoginModal(true);
                        }}
                        className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-6 py-3 rounded-2xl hover:from-blue-600 hover:to-purple-700 shadow-lg transform hover:scale-105 transition-all duration-200"
                      >
                        Login to Add Comments
                      </button>
                    </div>
                  )}
                </div>

                {/* Audit Trail */}
                <div className="backdrop-blur-md bg-white/50 p-6 rounded-3xl border border-white/20">
                  <h3 className="font-bold text-gray-900 mb-4 flex items-center">
                    <Activity className="w-5 h-5 mr-2 text-blue-500" />
                    Audit Trail
                  </h3>
                  {auditLog[selectedRequest.id] && auditLog[selectedRequest.id].length > 0 ? (
                    <div className="space-y-3">
                      {auditLog[selectedRequest.id].map(entry => (
                        <div key={entry.id} className="flex items-center justify-between py-3 border-b border-gray-200/50 last:border-b-0">
                          <div className="flex items-center space-x-4">
                            <div className="w-3 h-3 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full animate-pulse"></div>
                            <span className="text-sm font-medium text-gray-700">{entry.action}</span>
                            <span className="text-sm text-gray-500">
                              by {initialMockUsers[Object.keys(initialMockUsers).find(k => initialMockUsers[k].id === entry.userId)]?.name}
                            </span>
                          </div>
                          <span className="text-xs text-gray-500 flex items-center">
                            <Clock className="w-3 h-3 mr-1" />
                            {new Date(entry.timestamp).toLocaleString()}
                          </span>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <Activity className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                      <p>No audit entries</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Create Request Modal */}
      {showCreateRequest && (
        <div
          className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-start justify-center z-50 p-4 overflow-y-auto"
          onClick={(e) => {
            if (e.target === e.currentTarget) {
              setShowCreateRequest(false);
            }
          }}
        >
          <div
            className="backdrop-blur-xl bg-white/95 rounded-3xl shadow-2xl max-w-3xl w-full my-8 border border-white/20"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="bg-gradient-to-r from-green-500 to-emerald-600 text-white p-6 rounded-t-3xl">
              <div className="flex justify-between items-center">
                <div>
                  <h2 className="text-2xl font-bold">🏛️ Create New Withdrawal Request</h2>
                  <p className="text-green-100">
                    Logged in as: {currentUser?.name} ({currentUser?.role.replace('_', ' ')})
                    {currentUser?.role === 'archive' ? ' ✅ Authorized' : ' ❌ Not Authorized'}
                  </p>
                </div>
                <button
                  onClick={() => setShowCreateRequest(false)}
                  className="text-white hover:text-white bg-white/20 hover:bg-white/30 p-3 rounded-2xl transition-all duration-200 flex items-center justify-center"
                  title="Close"
                >
                  <span className="text-xl font-bold">✕</span>
                </button>
              </div>
            </div>

            <div className="p-8">
              {currentUser?.role !== 'archive' ? (
                <div className="bg-red-50 border-2 border-red-200 rounded-3xl p-8 text-center">
                  <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <Lock className="w-8 h-8 text-red-500" />
                  </div>
                  <h3 className="text-xl font-bold text-red-800 mb-2">Access Denied</h3>
                  <p className="text-red-700 mb-4">
                    Only <strong>Archive Team</strong> members can create new withdrawal requests.
                  </p>
                  <p className="text-red-600 text-sm">
                    Your role: <strong>{currentUser?.role.replace('_', ' ')}</strong> - Not authorized for this action.
                  </p>
                  <button
                    onClick={() => setShowCreateRequest(false)}
                    className="mt-4 bg-red-600 text-white px-6 py-3 rounded-2xl hover:bg-red-700 transition-all duration-200"
                  >
                    Close
                  </button>
                </div>
              ) : (
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">Project Number</label>
                      <input
                        type="text"
                        name="projectNumber"
                        value={newRequestData.projectNumber}
                        onChange={handleNewRequestChange}
                        className="w-full bg-white/70 border border-white/20 rounded-2xl px-4 py-3 text-gray-700 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-green-500 backdrop-blur-md"
                        placeholder="e.g., EGY-2024-001"
                        disabled={isProcessingOCR}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">Country</label>
                      <select
                        name="country"
                        value={newRequestData.country}
                        onChange={handleNewRequestChange}
                        className="w-full bg-white/70 border border-white/20 rounded-2xl px-4 py-3 text-gray-700 focus:outline-none focus:ring-2 focus:ring-green-500 backdrop-blur-md"
                        disabled={isProcessingOCR}
                      >
                        <option value="">Select Country</option>
                        <optgroup label="🌍 North Africa">
                          <option value="Egypt">Egypt</option>
                          <option value="Libya">Libya</option>
                          <option value="Tunisia">Tunisia</option>
                          <option value="Algeria">Algeria</option>
                          <option value="Morocco">Morocco</option>
                          <option value="Sudan">Sudan</option>
                        </optgroup>
                        <optgroup label="🌍 Central Africa">
                          <option value="Chad">Chad</option>
                          <option value="Central African Republic">Central African Republic</option>
                          <option value="Democratic Republic of Congo">Democratic Republic of Congo</option>
                          <option value="Cameroon">Cameroon</option>
                          <option value="Nigeria">Nigeria</option>
                          <option value="Kenya">Kenya</option>
                        </optgroup>
                        <optgroup label="🌏 South East Asia">
                          <option value="Malaysia">Malaysia</option>
                          <option value="Indonesia">Indonesia</option>
                          <option value="Thailand">Thailand</option>
                          <option value="Vietnam">Vietnam</option>
                          <option value="Philippines">Philippines</option>
                          <option value="Singapore">Singapore</option>
                        </optgroup>
                        <optgroup label="🌏 Central Asia">
                          <option value="Kazakhstan">Kazakhstan</option>
                          <option value="Uzbekistan">Uzbekistan</option>
                          <option value="Kyrgyzstan">Kyrgyzstan</option>
                          <option value="Tajikistan">Tajikistan</option>
                          <option value="Afghanistan">Afghanistan</option>
                          <option value="Pakistan">Pakistan</option>
                        </optgroup>
                      </select>
                      {newRequestData.country && (
                        <div className="mt-2 p-2 bg-blue-50 rounded-xl text-sm text-blue-700">
                          <strong>Region:</strong> {regionMapping[newRequestData.country]} •
                          <strong> Assigned to:</strong> {getOperationsTeamByRegion(newRequestData.country)?.name}
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">Reference Number</label>
                      <input
                        type="text"
                        name="refNumber"
                        value={newRequestData.refNumber}
                        onChange={handleNewRequestChange}
                        className="w-full bg-white/70 border border-white/20 rounded-2xl px-4 py-3 text-gray-700 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-green-500 backdrop-blur-md"
                        placeholder="e.g., WR/EGY/001/2024"
                        disabled={isProcessingOCR}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">Beneficiary Name</label>
                      <input
                        type="text"
                        name="beneficiaryName"
                        value={newRequestData.beneficiaryName}
                        onChange={handleNewRequestChange}
                        className="w-full bg-white/70 border border-white/20 rounded-2xl px-4 py-3 text-gray-700 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-green-500 backdrop-blur-md"
                        placeholder="e.g., Egyptian Infrastructure Development Company"
                        disabled={isProcessingOCR}
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">Amount</label>
                      <input
                        type="number"
                        name="amount"
                        value={newRequestData.amount}
                        onChange={handleNewRequestChange}
                        className="w-full bg-white/70 border border-white/20 rounded-2xl px-4 py-3 text-gray-700 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-green-500 backdrop-blur-md"
                        placeholder="e.g., 2500000"
                        disabled={isProcessingOCR}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">Currency</label>
                      <select
                        name="currency"
                        value={newRequestData.currency}
                        onChange={handleNewRequestChange}
                        className="w-full bg-white/70 border border-white/20 rounded-2xl px-4 py-3 text-gray-700 focus:outline-none focus:ring-2 focus:ring-green-500 backdrop-blur-md"
                        disabled={isProcessingOCR}
                      >
                        <option value="USD">USD</option>
                        <option value="EUR">EUR</option>
                        <option value="GBP">GBP</option>
                      </select>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">Value Date</label>
                      <input
                        type="date"
                        name="valueDate"
                        value={newRequestData.valueDate}
                        onChange={handleNewRequestChange}
                        className="w-full bg-white/70 border border-white/20 rounded-2xl px-4 py-3 text-gray-700 focus:outline-none focus:ring-2 focus:ring-green-500 backdrop-blur-md"
                        disabled={isProcessingOCR}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-semibold text-gray-700 mb-2">Priority</label>
                      <select
                        name="priority"
                        value={newRequestData.priority}
                        onChange={handleNewRequestChange}
                        className="w-full bg-white/70 border border-white/20 rounded-2xl px-4 py-3 text-gray-700 focus:outline-none focus:ring-2 focus:ring-green-500 backdrop-blur-md"
                        disabled={isProcessingOCR}
                      >
                        <option value="low">Low</option>
                        <option value="medium">Medium</option>
                        <option value="high">High</option>
                        <option value="urgent">Urgent</option>
                      </select>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-semibold text-gray-700 mb-2">
                      📄 Abu Dhabi Fund Withdrawal Request Form (OCR Enabled)
                    </label>

                    {/* OCR Demo Info */}
                    <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-2xl p-4 mb-4">
                      <h4 className="font-bold text-blue-800 mb-2">🔍 AI-Powered OCR Processing</h4>
                      <div className="text-sm text-blue-700 space-y-1">
                        <div>✨ <strong>Intelligent Extraction:</strong> Automatically reads withdrawal request forms</div>
                        <div>🎯 <strong>Auto-Assignment:</strong> Routes to correct regional operations team</div>
                        <div>📝 <strong>Form Population:</strong> Fills all fields automatically from uploaded document</div>
                        <div>🚀 <strong>Demo Files:</strong> Try "withdrawal_egypt_001.pdf", "withdrawal_malaysia_002.pdf", etc.</div>
                      </div>
                    </div>

                    {/* File Upload Area */}
                    <label htmlFor="document-upload" className={`border-2 border-dashed rounded-3xl p-8 text-center hover:border-green-400 transition-all duration-200 cursor-pointer group block ${
                      isProcessingOCR
                        ? 'border-blue-400 bg-blue-50/70 animate-pulse'
                        : 'border-green-300 bg-green-50/50 hover:bg-green-50/70'
                    }`}>
                      <div className="transform group-hover:scale-110 transition-transform duration-200">
                        {isProcessingOCR ? (
                          <div className="space-y-4">
                            <div className="w-16 h-16 bg-blue-500 rounded-full flex items-center justify-center mx-auto animate-spin">
                              <div className="w-8 h-8 bg-white rounded-full"></div>
                            </div>
                            <div className="space-y-2">
                              <p className="text-lg font-bold text-blue-700">🔍 OCR Processing...</p>
                              <p className="text-sm text-blue-600">Extracting data from withdrawal request form</p>
                              <div className="w-full bg-blue-200 rounded-full h-2">
                                <div className="bg-blue-600 h-2 rounded-full animate-pulse" style={{width: '66%'}}></div>
                              </div>
                            </div>
                          </div>
                        ) : (
                          <div>
                            <Upload className="w-12 h-12 text-green-500 mx-auto mb-4" />
                            <p className="text-lg font-medium text-gray-700 mb-2">Upload Official Withdrawal Request Form</p>
                            <p className="text-sm text-gray-500">PDF format • Auto-extracts all data • Regional auto-assignment</p>
                          </div>
                        )}
                      </div>
                      <input
                        id="document-upload"
                        type="file"
                        accept=".pdf"
                        onChange={handleNewRequestDocumentUpload}
                        className="hidden"
                        disabled={isProcessingOCR}
                      />
                    </label>

                    {/* OCR Results Display */}
                    {showOCRDemo && ocrResults && (
                      <div className="mt-4 p-4 bg-gradient-to-r from-emerald-50 to-green-50 border border-emerald-200 rounded-2xl">
                        <h5 className="font-bold text-emerald-800 mb-3">✅ OCR Extraction Complete!</h5>
                        <div className="grid grid-cols-2 gap-3 text-sm">
                          <div><strong>Project:</strong> {ocrResults.projectNumber}</div>
                          <div><strong>Country:</strong> {ocrResults.country}</div>
                          <div><strong>Beneficiary:</strong> {ocrResults.beneficiaryName}</div>
                          <div><strong>Amount:</strong> {parseFloat(ocrResults.amount).toLocaleString()} {ocrResults.currency}</div>
                          <div><strong>Region:</strong> {regionMapping[ocrResults.country]}</div>
                          <div><strong>Assigned to:</strong> {getOperationsTeamByRegion(ocrResults.country)?.name}</div>
                        </div>
                      </div>
                    )}

                    {/* Uploaded Files Display */}
                    {newRequestData.documents.length > 0 && (
                      <div className="mt-4 space-y-2">
                        <p className="text-sm font-medium text-gray-700">📁 Processed Documents:</p>
                        {newRequestData.documents.map((doc, index) => (
                          <div key={index} className="flex items-center justify-between bg-emerald-50 border border-emerald-200 p-3 rounded-xl text-sm text-emerald-800">
                            <span className="flex items-center space-x-2">
                              <span>✅</span>
                              <span>{doc.filename} ({doc.fileSize})</span>
                            </span>
                            <span className="text-xs text-emerald-600">OCR Processed</span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  <div className="flex justify-end space-x-4 pt-6">
                    <button
                      onClick={() => setShowCreateRequest(false)}
                      className="px-6 py-3 border border-gray-300 rounded-2xl text-gray-700 hover:bg-gray-50 transition-all duration-200"
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleCreateRequest}
                      disabled={isProcessingOCR}
                      className={`px-6 py-3 rounded-2xl shadow-lg transform hover:scale-105 transition-all duration-200 ${
                        isProcessingOCR
                          ? 'bg-gray-400 text-gray-600 cursor-not-allowed'
                          : 'bg-gradient-to-r from-green-500 to-emerald-600 text-white hover:from-green-600 hover:to-emerald-700'
                      }`}
                    >
                      {isProcessingOCR ? 'Processing OCR...' : 'Create Request'}
                    </button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Custom Message Modal */}
      {showMessageModal && (
        <MessageModal
          title={messageModalTitle}
          message={messageModalMessage}
          type={messageModalType}
          onClose={() => setShowMessageModal(false)}
        />
      )}
    </div>
  );
};

export default WithdrawalRequestTracker;

